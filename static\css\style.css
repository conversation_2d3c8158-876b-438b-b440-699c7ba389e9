body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    background-color: #f0f2f5;
    color: #333;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

header {
    background-color: #fff;
    padding: 0 24px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    flex-shrink: 0;
    box-shadow: 0 2px 8px #f0f1f2;
    z-index: 10;
}

header h1 {
    font-size: 20px;
    margin: 0;
    color: #1a1a1a;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.system-status {
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

#status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.status-stopped { background-color: #f5222d; }
.status-running { background-color: #52c41a; }
.status-loading { background-color: #faad14; }


.control-button {
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.control-button:hover {
    border-color: #40a9ff;
    color: #40a9ff;
}

#start-all-tasks {
    background-color: #1890ff;
    color: #fff;
    border-color: #1890ff;
}
#start-all-tasks:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}

#stop-all-tasks {
    background-color: #ff4d4f;
    color: white;
    border-color: #ff4d4f;
}
#stop-all-tasks:hover {
    background-color: #ff7875;
    border-color: #ff7875;
}

.control-button.danger-btn {
    background-color: #ff4d4f;
    color: white;
    border-color: #ff4d4f;
}

.control-button.danger-btn:hover {
    background-color: #ff7875;
    border-color: #ff7875;
    color: white;
}


.container {
    display: flex;
    flex-grow: 1;
    overflow: hidden;
}

aside {
    width: 200px;
    background-color: #fff;
    border-right: 1px solid #e8e8e8;
    padding-top: 24px;
    flex-shrink: 0;
}

aside nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

aside nav ul li a {
    display: block;
    padding: 12px 24px;
    text-decoration: none;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    border-right: 3px solid transparent;
    transition: background-color 0.3s, color 0.3s;
}

aside nav ul li a:hover {
    background-color: #e6f7ff;
}

aside nav ul li a.active {
    color: #1890ff;
    background-color: #e6f7ff;
    border-right-color: #1890ff;
    font-weight: 600;
}

main {
    flex-grow: 1;
    padding: 24px;
    overflow-y: auto;
    background-color: #f0f2f5;
}

/* Basic styles for content sections */
.content-section {
    display: none;
    animation: fadeIn 0.5s;
    background-color: #fff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.09);
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

h2 {
    font-size: 22px;
    margin-top: 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 16px;
    color: #1a1a1a;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.section-header h2 {
    margin: 0;
    border: none;
    padding: 0;
}

.primary-btn {
    background-color: #1890ff;
    color: #fff;
    border-color: #1890ff;
}
.primary-btn:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    color: #fff;
}


/* Task Table Styles */
.tasks-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.tasks-table th, .tasks-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.tasks-table th {
    background-color: #fafafa;
    font-weight: 600;
    color: #555;
}

.tasks-table tbody tr:hover {
    background-color: #f5faff;
}

.tag {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    color: #096dd9;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.tag.personal {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #389e0d;
}

.action-btn {
    background: none;
    border: none;
    color: #1890ff;
    cursor: pointer;
    padding: 4px 8px;
    font-size: 14px;
    text-decoration: none;
}
.action-btn:hover {
    text-decoration: underline;
}
.action-btn.delete-btn {
    color: #ff4d4f;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #52c41a;
}

input:focus + .slider {
  box-shadow: 0 0 1px #52c41a;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Styles for inline editing in tasks table */
.tasks-table tr.editing td {
    padding-top: 8px;
    padding-bottom: 8px;
    vertical-align: middle;
}

.tasks-table input[type="text"] {
    width: 95%;
    padding: 6px 8px;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.tasks-table input[type="text"]:focus {
    border-color: #40a9ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.tasks-table select {
    width: 100%;
    padding: 6px 8px;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: border-color 0.3s;
    box-sizing: border-box;
    background-color: #fff;
}

.tasks-table select:focus {
    border-color: #40a9ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.tasks-table .save-btn {
    color: #52c41a;
}
.tasks-table .save-btn:hover {
    text-decoration: underline;
    color: #52c41a;
}

.tasks-table .cancel-btn {
    color: #faad14;
}
.tasks-table .cancel-btn:hover {
    text-decoration: underline;
    color: #faad14;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    width: 90%;
    max-width: 680px;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    transform: scale(0.95);
    transition: transform 0.3s;
}

.modal-overlay.visible .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    border: none;
    padding: 0;
}

.close-button {
    background: none;
    border: none;
    font-size: 28px;
    cursor: pointer;
    color: #888;
    line-height: 1;
}
.close-button:hover {
    color: #333;
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    font-size: 14px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group textarea:focus {
    border-color: #40a9ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-group-inline {
    display: flex;
    align-items: flex-end;
    gap: 16px;
}
.form-group-inline > div {
    flex-grow: 1;
}
.form-group-inline > span {
    padding-bottom: 10px;
}


.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.log-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Log Viewer Styles */
#log-content-container {
    background-color: #2b2b2b;
    color: #f0f0f0;
    padding: 15px;
    border-radius: 5px;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 70vh;
    overflow-y: auto;
    font-family: "SF Mono", "Consolas", "Menlo", monospace;
    font-size: 13px;
    line-height: 1.6;
}

/* Spinner for button */
.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    -webkit-animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { -webkit-transform: rotate(360deg); }
}
@-webkit-keyframes spin {
    to { -webkit-transform: rotate(360deg); }
}

/* Results View Styles */
.results-filter-bar {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 24px;
    background-color: #fafafa;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
}

.results-filter-bar select {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    font-size: 14px;
    background-color: #fff;
    min-width: 200px;
}

.results-filter-bar label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    cursor: pointer;
}

#results-grid-container {
    padding-top: 8px;
}

#results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.result-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.06);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s, box-shadow 0.2s;
}

.result-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.card-image {
    width: 100%;
    height: 200px;
    background-color: #f0f0f0;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}
.result-card:hover .card-image img {
    transform: scale(1.05);
}

.card-content {
    padding: 16px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px;
    line-height: 1.4;
    color: #1a1a1a;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 44.8px; /* 16px * 1.4 * 2 */
}
.card-title a {
    color: inherit;
    text-decoration: none;
}
.card-title a:hover {
    color: #1890ff;
}

.card-price {
    font-size: 20px;
    font-weight: bold;
    color: #ff4d4f;
    margin: 0 0 12px;
}

.card-ai-summary {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 4px;
    padding: 12px;
    font-size: 13px;
    margin-bottom: 12px;
    flex-grow: 1;
}

.card-ai-summary.not-recommended {
    background-color: #fff1f0;
    border-color: #ffa39e;
}

.card-ai-summary strong {
    display: block;
    margin-bottom: 4px;
    color: #389e0d;
}
.card-ai-summary.not-recommended strong {
    color: #cf1322;
}

.card-ai-summary p {
    margin: 0;
    color: #555;
    white-space: pre-wrap; /* Allow wrapping */
    word-break: break-word; /* Break long words */
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    color: #888;
    border-top: 1px solid #f0f0f0;
    padding-top: 12px;
    margin-top: auto;
    gap: 16px;
}

.card-footer > div {
    min-width: 0; /* Allow flex item to shrink and text to ellipsis */
}

.seller-info {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    color: #555;
    margin-bottom: 4px;
}

.time-info p {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
}

.card-footer .action-btn {
    flex-shrink: 0; /* Prevent the button from shrinking */
}

.view-json-btn {
    font-size: 13px;
}

/* JSON Viewer Modal */
#json-viewer-modal .modal-content {
    max-width: 800px;
}

#json-viewer-content {
    background-color: #2b2b2b;
    color: #f0f0f0;
    padding: 15px;
    border-radius: 5px;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 60vh;
    overflow-y: auto;
    font-family: "SF Mono", "Consolas", "Menlo", monospace;
    font-size: 13px;
    line-height: 1.6;
}

/* Settings Page Styles */
.settings-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.09);
    padding: 24px;
    margin-bottom: 24px;
}

.settings-card h3 {
    font-size: 18px;
    margin-top: 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
}

.status-list {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f5f5f5;
}
.status-item:last-child {
    border-bottom: none;
}

.status-item .label {
    font-weight: 500;
    color: #555;
}

.status-item .value .tag {
    font-weight: normal;
}

.status-ok {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #389e0d;
}

.status-error {
    background-color: #fff1f0;
    border-color: #ffa39e;
    color: #cf1322;
}

.prompt-manager {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.prompt-list-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.prompt-list-container label {
    font-size: 14px;
    font-weight: 500;
}

#prompt-selector {
    min-width: 250px;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    font-size: 14px;
    background-color: #fff;
}

.prompt-editor-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

#prompt-editor {
    width: 100%;
    min-height: 400px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 12px;
    font-family: "SF Mono", "Consolas", "Menlo", monospace;
    font-size: 13px;
    line-height: 1.6;
    resize: vertical;
    box-sizing: border-box;
}

#prompt-editor:focus {
    border-color: #40a9ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

#save-prompt-btn {
    align-self: flex-end;
}
